#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极简版Selenium脚本 (硬编码参数):
根据用户反馈，优化输出日志，减少等待时间（更多依赖智能等待），并支持无头模式运行。
修正登录后跳转逻辑：登录后主动导航至座位系统URL。
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

class SeleniumClickAutomator:
    """极简的Selenium自动化点击类"""
    
    def __init__(self, headless=False):
        self.driver = None
        self.headless = headless
        
    def setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument('--headless')
        
        # 常见无头模式所需参数
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        # 模拟手机User-Agent，保持与移动端行为一致
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ 浏览器启动成功。")
            return True
        except WebDriverException as e:
            print(f"❌ 浏览器启动失败: {e}")
            print("💡 请确保已安装ChromeDriver并添加到PATH，且与您的Chrome浏览器版本匹配。")
            return False
    
    def click_element(self, by_type, selector, timeout=10, description="元素", print_success=False):
        """辅助函数：等待元素出现并点击"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by_type, selector)) 
            )
            self.driver.execute_script("arguments[0].click();", element)
            if print_success:
                print(f"✅ 已点击: {description}")
            return True
        except TimeoutException:
            print(f"❌ 元素未在 {timeout} 秒内出现或可点击: {description} (Selector: {selector})")
            return False
        except Exception as e:
            print(f"❌ 点击 {description} 时发生错误: {e}")
            return False

    def type_text(self, by_type, selector, text, timeout=10, description="输入框", print_success=False):
        """辅助函数：等待输入框出现并输入文本"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by_type, selector))
            )
            element.clear()
            element.send_keys(text)
            if print_success:
                print(f"✅ 已输入文本到: {description}")
            return True
        except TimeoutException:
            print(f"❌ 输入框未在 {timeout} 秒内出现: {description} (Selector: {selector})")
            return False
        except Exception as e:
            print(f"❌ 输入文本到 {description} 时发生错误: {e}")
            return False

    def wait_for_url_contains(self, text, timeout=10, description="页面跳转"):
        """等待URL包含特定文本，用于判断页面是否成功跳转"""
        try:
            WebDriverWait(self.driver, timeout).until(
                EC.url_contains(text)
            )
            return True
        except TimeoutException:
            print(f"❌ {description}超时，URL未在 {timeout} 秒内包含: '{text}' (Current URL: {self.driver.current_url})")
            return False
        except Exception as e:
            print(f"❌ 等待URL时发生错误: {e}")
            return False

    def simulate_login_and_seat_booking(self, username, password, room_id, seat_num, start_time, end_time):
        """模拟完整的登录和座位预约点击流程"""
        if not self.setup_driver():
            return False

        try:
            # --- 1. 登录页面操作 ---
            print("🌐 正在打开登录页面并尝试登录...")
            self.driver.get("https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid=")
            
            if not self.type_text(By.ID, "phone", username, description="用户名输入框"): return False
            if not self.type_text(By.ID, "pwd", password, description="密码输入框"): return False

            if not self.click_element(By.XPATH, "//span[contains(@class, 'checkBox') or contains(@class, 'check')] | //input[@type='checkbox']", description="同意政策复选框"): return False

            if not self.click_element(By.XPATH, "//button[contains(@class, 'btn-big-blue') and text()='登录']", description="登录按钮"): return False
            
            # 登录后，系统可能先跳转到用户主页，我们主动导航到座位系统URL
            print("✅ 登录操作完成，尝试导航至座位系统主页...")
            # 导航到座位系统主页
            self.driver.get(f"https://office.chaoxing.com/front/third/apps/seat/index?fidEnc=a76ca29f42cc102d")
            
            # 等待确认已经到达座位系统页面
            if not self.wait_for_url_contains("office.chaoxing.com/front/third/apps/seat/index", description="座位系统页面加载"):
                return False
            
            # 给页面留一点时间加载所有JS和内容
            time.sleep(1) # 短暂等待
            print("✅ 已成功导航并加载座位系统页面。")

            # --- 2. 座位预约页面操作 ---
            print("⏳ 正在执行预约流程...")
            
            # 点击“找空位”或“当日预约”按钮 (sind_top_btn)
            if not self.click_element(By.XPATH, "/html/body/div[1]/ul/li[1]", description="找空位/当日预约按钮 (sind_top_btn)", print_success=True): 
                return False
            
            # 点击“预约”按钮 (scr_sch)
            if not self.click_element(By.XPATH, "/html/body/div/ul[1]/li[2]/span", description="预约按钮 (scr_sch)"): return False
            
            # 选择开始时间
            if not self.click_element(By.XPATH, f"//li[contains(@class, 'time_cell') and text()='{start_time}']", description=f"开始时间 {start_time}"): return False

            # 选择结束时间
            if not self.click_element(By.XPATH, f"//li[contains(@class, 'time_cell') and text()='{end_time}']", description=f"结束时间 {end_time}"): return False

            # 点击时间选择确认按钮
            if not self.click_element(By.XPATH, "//span[contains(@class, 'time_sure') and text()='确认']", description="时间确认按钮"): return False
            
            # 选择座位号
            if not self.click_element(By.XPATH, f"//li[contains(@class, 'order_per') and .//p[contains(@class, 'order_num') and text()='{seat_num}']]", description=f"座位号 {seat_num}"): return False

            # 点击提交按钮
            if not self.click_element(By.XPATH, "//p[contains(@class, 'order_submit') and text()='提交']", description="提交按钮"): return False
            
            # 最后的等待，确保请求发送和处理完成
            time.sleep(2) 

            print("🎉 自动化点击流程完成。")
            return True

        except Exception as e:
            print(f"❌ 自动化过程中发生未知错误: {e}")
            return False

    def cleanup(self):
        """清理资源，关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {e}")

def main():
    """主函数"""
    print("="*60)
    print("🚀 极简Selenium座位预约模拟点击脚本 (优化版)")
    print("="*60)
    
    # --- 用户在此处修改以下变量来自定义脚本 ---
    YOUR_USERNAME = "17607062006"     # <--- 在这里填入你的用户名/手机号
    YOUR_PASSWORD = "ab.135246"       # <--- 在这里填入你的密码
    YOUR_ROOM_ID = "8886"             # <--- 房间ID (目前仅用于打印，未在URL中动态使用)
    YOUR_SEAT_NUM = "223"             # <--- 你的目标座位号 (请确保与页面显示完全一致)
    YOUR_START_TIME = "09:00-09:30"   # <--- 你的预约开始时间 (请确保与页面显示完全一致)
    YOUR_END_TIME = "20:30-21:00"     # <--- 你的预约结束时间 (请确保与页面显示完全一致)
    # ----------------------------------------
    
    # 检查用户是否修改了默认值
    if YOUR_USERNAME == "你的用户名或手机号" or YOUR_PASSWORD == "你的密码":
        print("🚨 警告：请在代码中修改 YOUR_USERNAME 和 YOUR_PASSWORD 为您的实际信息！")
        return

    # 创建自动化器实例 (改为headless=True以在后台运行)
    automator = SeleniumClickAutomator(headless=True) # <-- 设置为 True 启用无头模式
    
    # 执行自动化点击流程
    success = automator.simulate_login_and_seat_booking(
        YOUR_USERNAME, 
        YOUR_PASSWORD, 
        YOUR_ROOM_ID,  
        YOUR_SEAT_NUM, 
        YOUR_START_TIME, 
        YOUR_END_TIME
    )
    
    # 清理资源
    automator.cleanup()
    
    if success:
        print("\n✅ 脚本执行完毕：预约流程貌似已成功完成。")
    else:
        print("\n❌ 脚本执行完毕：预约流程未能完全成功。请检查上面的错误日志。")

if __name__ == "__main__":
    main()
