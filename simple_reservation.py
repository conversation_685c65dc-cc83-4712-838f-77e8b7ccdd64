#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的座位预约系统 - 严格按照三步逻辑
1. 登录后直接打开指定网页
2. 获取完整的20个Cookie字段
3. 使用Cookie进行预约
"""

import time
import json
import requests
import urllib3
from datetime import datetime, timedelta

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    SELENIUM_AVAILABLE = True
    print("✅ Selenium库已导入")
except ImportError:
    SELENIUM_AVAILABLE = False
    print("❌ Selenium库未安装")

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SimpleReservationSystem:
    """简化的座位预约系统"""
    
    def __init__(self):
        self.driver = None
        self.cookies = {}
        self.session = requests.Session()
        
        # 目标Cookie列表
        self.target_cookies = [
            'fid', 'xxtenc', '_uid', 'UID', 'tl', '_d', 'vc3', 'uf',
            'cx_p_token', 'p_auth_token', 'DSSTASH_LOG', 'source',
            'spaceFid', 'spaceRoleId', 'route', 'JSESSIONID',
            'oa_uid', 'oa_name', 'oa_deptid', 'oa_enc'
        ]
    
    def setup_driver(self):
        """设置Chrome驱动"""
        if not SELENIUM_AVAILABLE:
            raise Exception("Selenium未安装")
            
        chrome_options = Options()
        # chrome_options.add_argument('--headless')  # 关闭无头模式方便调试
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Chrome浏览器启动成功")
            return True
        except Exception as e:
            print(f"❌ Chrome浏览器启动失败: {e}")
            return False
    
    def step1_login_and_navigate(self, username, password):
        """第一步：登录并直接打开指定网页"""
        print("="*80)
        print("🚀 第一步：登录并访问座位系统")
        print("="*80)
        
        if not self.setup_driver():
            return False
            
        try:
            # 登录
            print("🌐 正在打开登录页面...")
            self.driver.get("https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid=")
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "phone"))
            )
            
            print("📱 正在输入用户名...")
            username_input = self.driver.find_element(By.ID, "phone")
            username_input.clear()
            username_input.send_keys(username)
            
            print("🔐 正在输入密码...")
            password_input = self.driver.find_element(By.ID, "pwd")
            password_input.clear()
            password_input.send_keys(password)
            
            # 勾选复选框
            print("☑️ 正在勾选复选框...")
            try:
                checkbox = self.driver.find_element(By.CLASS_NAME, "checkBox")
                self.driver.execute_script("arguments[0].click();", checkbox)
                print("✅ 复选框已勾选")
            except Exception as e:
                print(f"⚠️ 复选框处理失败: {e}")
            
            # 等待一下再点击登录
            time.sleep(2)
            
            print("🚀 正在提交登录...")
            # 尝试多种方式找到登录按钮
            login_button = None
            try:
                login_button = self.driver.find_element(By.ID, "loginBtn")
            except NoSuchElementException:
                try:
                    login_button = self.driver.find_element(By.CLASS_NAME, "loginBtn")
                except NoSuchElementException:
                    try:
                        login_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")
                    except NoSuchElementException:
                        try:
                            login_button = self.driver.find_element(By.XPATH, "//input[@type='submit']")
                        except NoSuchElementException:
                            raise Exception("找不到登录按钮")

            if login_button:
                self.driver.execute_script("arguments[0].click();", login_button)
                print("✅ 登录按钮已点击")
            
            # 等待登录完成
            print("⏳ 等待登录完成...")
            time.sleep(5)
            
            # 检查登录是否成功
            current_url = self.driver.current_url or ""
            if current_url and "passport2.chaoxing.com" in current_url and "mlogin" in current_url:
                print("❌ 登录失败，仍在登录页面")
                return False
            
            print("✅ 登录成功！")
            
            # 直接访问座位系统主页
            print("🏢 正在访问座位系统主页...")
            self.driver.get("https://office.chaoxing.com/front/third/apps/seat/index?fidEnc=a76ca29f42cc102d")
            
            print("⏳ 等待页面完全加载...")
            time.sleep(10)  # 给足够时间让页面完全加载
            
            print("✅ 第一步完成：已成功访问座位系统主页")
            return True
            
        except Exception as e:
            print(f"❌ 第一步失败: {e}")
            return False
    
    def step2_get_all_cookies(self):
        """第二步：获取完整的20个Cookie字段"""
        print("\n" + "="*80)
        print("🍪 第二步：获取完整Cookie")
        print("="*80)
        
        try:
            # 获取所有Cookie
            selenium_cookies = self.driver.get_cookies()
            for cookie in selenium_cookies:
                self.cookies[cookie['name']] = cookie['value']
            
            print(f"🍪 总共获取到 {len(self.cookies)} 个Cookie")
            
            # 检查目标Cookie
            found_cookies = []
            missing_cookies = []
            
            for cookie_name in self.target_cookies:
                if cookie_name in self.cookies:
                    found_cookies.append(cookie_name)
                    value = self.cookies[cookie_name]
                    print(f"✅ Name={cookie_name}; Value={value}")
                else:
                    missing_cookies.append(cookie_name)
            
            print(f"\n📊 Cookie获取统计:")
            print(f"   ✅ 成功获取: {len(found_cookies)}/20 个")
            print(f"   ❌ 缺失: {len(missing_cookies)} 个")
            
            if missing_cookies:
                print(f"   缺失的Cookie: {', '.join(missing_cookies)}")
            
            if len(found_cookies) >= 17:  # 至少获取17个关键Cookie
                print("🎉 所有关键Cookie获取成功！")
                return True
            else:
                print("❌ 关键Cookie获取不足")
                return False
                
        except Exception as e:
            print(f"❌ 第二步失败: {e}")
            return False
    
    def step3_make_reservation(self, room_id, seat_num, start_time, end_time, date_str):
        """第三步：使用Cookie进行预约"""
        print("\n" + "="*80)
        print("🎯 第三步：进行座位预约")
        print("="*80)
        
        try:
            # 关闭浏览器，释放资源
            if self.driver:
                self.driver.quit()
                print("🔄 浏览器已关闭")
            
            # 设置requests session的Cookie
            for name, value in self.cookies.items():
                self.session.cookies.set(name, value)
            
            # 设置请求头
            self.session.headers.update({
                'sec-ch-ua-platform': '"Android"',
                'x-requested-with': 'XMLHttpRequest',
                'user-agent': 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?1',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-mode': 'cors',
                'sec-fetch-dest': 'empty',
                'referer': f'https://office.chaoxing.com/front/third/apps/seat/select?deptIdEnc=a76ca29f42cc102d&id={room_id}&day={date_str}&backLevel=2&fidEnc=a76ca29f42cc102d',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'priority': 'u=1, i',
                'host': 'office.chaoxing.com'
            })
            
            # 获取token和enc（这里使用示例值，实际应该动态获取）
            token = "6bd25321165e4a8a9478a1ee12b2771e"  # 示例token
            enc = "86d971d84daa68c2403b788d0df46ef8"    # 示例enc
            
            # 构建预约请求参数
            params = {
                'deptIdEnc': 'a76ca29f42cc102d',
                'roomId': room_id,
                'startTime': start_time,
                'endTime': end_time,
                'day': date_str,
                'seatNum': seat_num,
                'captcha': '',
                'token': token,
                'enc': enc
            }
            
            print(f"📋 预约参数:")
            for key, value in params.items():
                print(f"   {key}: {value}")
            
            # 发送预约请求
            url = "https://office.chaoxing.com/data/apps/seat/submit"
            print(f"\n🌐 发送预约请求到: {url}")
            
            response = self.session.get(url, params=params, verify=False, timeout=30)
            
            print(f"📊 响应结果:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    if result.get('success'):
                        print("🎉 座位预约成功！")
                        return True
                    else:
                        print(f"❌ 座位预约失败: {result}")
                        return False
                        
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效的JSON: {response.text}")
                    return False
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 第三步失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    print("="*80)
    print("🚀 简化座位预约系统")
    print("="*80)
    
    # 读取配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        user_config = config['reserve'][0]
        username = user_config['username']
        password = user_config['password']
        
        # 预约参数
        room_id = user_config['rooms'][0]['roomid']
        seat_num = user_config['rooms'][0]['seatid'][0]
        start_time = "21:00"
        end_time = "22:00"
        date_str = "2025-07-31"
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return
    
    # 创建预约系统
    system = SimpleReservationSystem()
    
    # 执行三步流程
    if system.step1_login_and_navigate(username, password):
        if system.step2_get_all_cookies():
            if system.step3_make_reservation(room_id, seat_num, start_time, end_time, date_str):
                print("\n🎉 整个预约流程成功完成！")
            else:
                print("\n❌ 预约失败")
        else:
            print("\n❌ Cookie获取失败")
    else:
        print("\n❌ 登录失败")

if __name__ == "__main__":
    main()
