import json
import time
import argparse
import os
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

from utils import reserve, get_user_credentials
get_current_time = lambda action: time.strftime("%H:%M:%S", time.localtime(time.time() + 8*3600)) if action else time.strftime("%H:%M:%S", time.localtime(time.time()))
get_current_dayofweek = lambda action: time.strftime("%A", time.localtime(time.time() + 8*3600)) if action else time.strftime("%A", time.localtime(time.time()))

SLEEPTIME = 0.2 # 每次抢座的间隔
ENABLE_SLIDER = False # 是否有滑块验证
MAX_ATTEMPT = 4 # 最大尝试次数，每个用户最多尝试5次
RESERVE_NEXT_DAY = False # 预约明天而不是今天的
DEBUG = False  # 是否输出详细调试信息，默认只输出简洁信息

# 仅支持 daysofweek 为数字1-7（1=周一，7=周日）
WEEKDAY_MAP = {
    1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday', 5: 'Friday', 6: 'Saturday', 7: 'Sunday',
    '1': 'Monday', '2': 'Tuesday', '3': 'Wednesday', '4': 'Thursday', '5': 'Friday', '6': 'Saturday', '7': 'Sunday'
}

def is_today_in_user_days(user, current_dayofweek):
    days = user.get('daysofweek', [])
    # 只允许数字
    mapped_days = [WEEKDAY_MAP[str(d)] for d in days]
    return current_dayofweek in mapped_days

def login_and_reserve(users, usernames, passwords, action, success_list=None):
    logging.info(f"全局设置: \n抢座间隔: {SLEEPTIME}\n滑块验证: {ENABLE_SLIDER}\n预约明天: {RESERVE_NEXT_DAY}")
    if action and len(usernames.split(",")) != len(users):
        raise Exception("用户数量应与配置数量一致")
    if success_list is None:
        success_list = [False] * len(users)
    current_dayofweek = get_current_dayofweek(action)
    for index, user in enumerate(users):
        username, password, times, roomid, seatid, daysofweek = user.values()
        if action:
            username, password = usernames.split(',')[index], passwords.split(',')[index]
        if(current_dayofweek not in daysofweek):
            logging.info("今天不在预约设置的日期内，跳过该用户")
            continue
        if not success_list[index]: 
            logging.info(f"----------- 用户: {username} -- 时间段: {times} -- 座位: {seatid} 开始尝试 -----------")
            s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
            s.get_login_status()
            s.login(username, password)
            s.requests.headers.update({'Host': 'office.chaoxing.com'})
            suc = s.submit(times, roomid, seatid, action)
            success_list[index] = suc
    return success_list

def main(users, action=False):
    current_time = get_current_time(action)
    logging.info(f"脚本启动时间 {current_time}，action {'开启' if action else '关闭'}")
    logging.info(f"全局设置: \n抢座间隔: {SLEEPTIME}\n滑块验证: {ENABLE_SLIDER}\n预约明天: {RESERVE_NEXT_DAY}")
    attempt_times = 0
    usernames, passwords = None, None
    if action:
        usernames, passwords = get_user_credentials(action)
    success_list = None
    current_dayofweek = get_current_dayofweek(action)
    today_reservation_num = sum(1 for d in users if is_today_in_user_days(d, current_dayofweek))
    user_attempts = [0] * len(users)
    while True:
        attempt_times += 1
        if success_list is None:
            success_list = [False] * len(users)
        for idx, user in enumerate(users):
            if success_list[idx]:
                continue
            if user_attempts[idx] >= MAX_ATTEMPT:
                continue
            if not is_today_in_user_days(user, current_dayofweek):
                if DEBUG:
                    logging.info("今天不在预约设置的日期内，跳过该用户")
                continue
            seatids = user['seatid'] if isinstance(user['seatid'], list) else [user['seatid']]
            if DEBUG:
                logging.info(f"----------- 用户: {user['username']} -- 时间段: {get_time_key(user)} -- 备选座位: {seatids} 第{user_attempts[idx]+1}/{MAX_ATTEMPT}次尝试 -----------")
            s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY, debug=DEBUG)
            s.get_login_status()
            username, password = user['username'], user['password']
            if action:
                username, password = usernames.split(',')[idx], passwords.split(',')[idx]
            s.login(username, password)
            s.requests.headers.update({'Host': 'office.chaoxing.com'})
            suc = False
            user_failed = False
            try:
                for seat in seatids:
                    if DEBUG:
                        logging.info(f"尝试座位: {seat}")
                    times = get_time_key(user)  # 确保用的是用户配置的时间段
                    result = s.submit(times, user['roomid'], [seat], action, return_detail=True)
                    if not DEBUG:
                        alias = user.get('alias', user['username'])
                        msg = result.get('msg') if isinstance(result, dict) else ''
                        success = result.get('success') if isinstance(result, dict) else result
                        params = result.get('params') if isinstance(result, dict) and 'params' in result else None
                        extra = ''
                        if msg == '该时间段已被占用！' and isinstance(result, dict):
                            seat_info = result.get('data', {}).get('seatReserve', {})
                            seat_num = seat_info.get('seatNum', params.get('seatNum') if params else '')
                            room_id = seat_info.get('roomId', params.get('roomId') if params else '')
                            room_name = seat_info.get('firstLevelName', '')
                            extra = f"\n  └─被占用位置: 房间ID:{room_id} 房间名:{room_name} 座位号:{seat_num}"
                        try:
                            alias_str = str(alias)
                            alias_str = alias_str.encode('utf-8').decode('utf-8')
                        except:
                            alias_str = alias
                        if params:
                            print(f"\n用户: {alias_str}\n  预约日期: {params.get('day')}\n  开始时间: {params.get('startTime')}\n  结束时间: {params.get('endTime')}\n  房间ID: {params.get('roomId')}\n  座位号: {params.get('seatNum')}\n  结果: {msg}{extra}\n  成功: {success}\n{'-'*40}")
                        else:
                            print(f"\n用户: {alias_str}\n  结果: {msg}{extra}\n  成功: {success}\n{'-'*40}")
                    suc = result if isinstance(result, bool) else result.get('success', False)
                    if isinstance(result, dict):
                        msg = result.get('msg', '')
                        if msg == '该时间段您已有预约！':
                            user_failed = True
                            break
                        if msg == '该时间段已过，不可预约！':
                            user_failed = True
                            break
                    if suc:
                        break
            except Exception as e:
                if '该时间段已有预约' in str(e) or '该时间段已过，不可预约' in str(e):
                    user_failed = True
                else:
                    raise
            success_list[idx] = suc
            user_attempts[idx] += 1
            # 用户失败只影响自己，不中断主循环
            if user_failed:
                continue
        if DEBUG:
            print(f"第{attempt_times}轮尝试，当前时间 {get_current_time(action)}，成功列表: {success_list}")
        if sum(success_list) == today_reservation_num:
            print(f"全部预约成功！")
            return
        if all(attempt >= MAX_ATTEMPT or suc for attempt, suc in zip(user_attempts, success_list)):
            print("所有用户已达最大尝试次数或已成功，脚本结束。")
            break

def debug(users, action=False):
    logging.info(f"全局设置: \n抢座间隔: {SLEEPTIME}\n滑块验证: {ENABLE_SLIDER}\n预约明天: {RESERVE_NEXT_DAY}")
    suc = False
    logging.info(f" Debug Mode start! , action {'开启' if action else '关闭'}")
    if action:
        usernames, passwords = get_user_credentials(action)
    current_dayofweek = get_current_dayofweek(action)
    for index, user in enumerate(users):
        username, password, times, roomid, seatid, daysofweek = user.values()
        if type(seatid) == str:
            seatid = [seatid]
        if action:
            username ,password = usernames.split(',')[index], passwords.split(',')[index]
        if(current_dayofweek not in daysofweek):
            logging.info("今天不在预约设置的日期内，跳过该用户")
            continue
        logging.info(f"----------- 用户: {username} -- 时间段: {times} -- 座位: {seatid} 开始尝试 -----------")
        s = reserve(sleep_time=SLEEPTIME,  max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
        s.get_login_status()
        s.login(username, password)
        s.requests.headers.update({'Host': 'office.chaoxing.com'})
        suc = s.submit(times, roomid, seatid, action)
        if suc:
            return

def get_roomid(args1, args2):
    username = input("请输入用户名：")
    password = input("请输入密码：")
    s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
    s.get_login_status()
    s.login(username=username, password=password)
    s.requests.headers.update({'Host': 'office.chaoxing.com'})
    encode = input("请输入deptldEnc：")
    s.roomid(encode)

def get_time_key(user):
    if 'times' in user:
        return user['times']
    elif 'time' in user:
        return user['time']
    elif '时间段' in user:
        return user['时间段']
    else:
        raise KeyError('用户配置缺少时间段字段（times/time/时间段）')

if __name__ == "__main__":
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    parser = argparse.ArgumentParser(prog='Chao Xing seat auto reserve')
    parser.add_argument('-u','--user', default=config_path, help='user config file')
    parser.add_argument('-m','--method', default="reserve" ,choices=["reserve", "debug", "room"], help='for debug')
    parser.add_argument('-a','--action', action="store_true",help='use --action to enable in github action')
    args = parser.parse_args()
    func_dict = {"reserve": main, "debug":debug, "room": get_roomid}
    with open(args.user, "r+") as data:
        usersdata = json.load(data)["reserve"]
    func_dict[args.method](usersdata, args.action)
