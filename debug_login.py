#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试登录页面元素
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def debug_login_page():
    """调试登录页面，查看页面结构"""
    
    chrome_options = Options()
    # 不使用无头模式，可以看到浏览器
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print("🌐 正在打开登录页面...")
        driver.get("https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid=")
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(3)
        
        print(f"📄 页面标题: {driver.title}")
        print(f"🌐 当前URL: {driver.current_url}")
        
        # 查找用户名输入框
        print("\n🔍 查找页面元素...")
        
        # 查找用户名输入框
        username_elements = []
        try:
            username_elements.append(("ID=phone", driver.find_element(By.ID, "phone")))
        except:
            pass
        try:
            username_elements.append(("NAME=phone", driver.find_element(By.NAME, "phone")))
        except:
            pass
        try:
            username_elements.append(("XPATH=//input[@placeholder*='手机号']", driver.find_element(By.XPATH, "//input[contains(@placeholder, '手机号')]")))
        except:
            pass
        
        print(f"📱 找到用户名输入框: {len(username_elements)} 个")
        for desc, elem in username_elements:
            print(f"   {desc}: {elem.tag_name}, visible={elem.is_displayed()}")
        
        # 查找密码输入框
        password_elements = []
        try:
            password_elements.append(("ID=pwd", driver.find_element(By.ID, "pwd")))
        except:
            pass
        try:
            password_elements.append(("NAME=pwd", driver.find_element(By.NAME, "pwd")))
        except:
            pass
        try:
            password_elements.append(("TYPE=password", driver.find_element(By.XPATH, "//input[@type='password']")))
        except:
            pass
        
        print(f"🔐 找到密码输入框: {len(password_elements)} 个")
        for desc, elem in password_elements:
            print(f"   {desc}: {elem.tag_name}, visible={elem.is_displayed()}")
        
        # 查找登录按钮
        login_buttons = []
        try:
            login_buttons.append(("ID=loginBtn", driver.find_element(By.ID, "loginBtn")))
        except:
            pass
        try:
            login_buttons.append(("CLASS=loginBtn", driver.find_element(By.CLASS_NAME, "loginBtn")))
        except:
            pass
        try:
            login_buttons.append(("TEXT=登录", driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")))
        except:
            pass
        try:
            login_buttons.append(("TYPE=submit", driver.find_element(By.XPATH, "//input[@type='submit']")))
        except:
            pass
        try:
            login_buttons.append(("BUTTON", driver.find_element(By.TAG_NAME, "button")))
        except:
            pass
        
        print(f"🚀 找到登录按钮: {len(login_buttons)} 个")
        for desc, elem in login_buttons:
            print(f"   {desc}: {elem.tag_name}, text='{elem.text}', visible={elem.is_displayed()}")
        
        # 如果找到了元素，尝试登录
        if username_elements and password_elements and login_buttons:
            print("\n🧪 尝试登录...")
            
            # 输入用户名
            username_elem = username_elements[0][1]
            username_elem.clear()
            username_elem.send_keys("17607062006")
            print("✅ 用户名已输入")
            
            # 输入密码
            password_elem = password_elements[0][1]
            password_elem.clear()
            password_elem.send_keys("ab.135246")
            print("✅ 密码已输入")
            
            # 点击登录
            login_elem = login_buttons[0][1]
            login_elem.click()
            print("✅ 登录按钮已点击")
            
            # 等待跳转
            print("⏳ 等待登录结果...")
            time.sleep(5)
            
            new_url = driver.current_url
            print(f"🌐 登录后URL: {new_url}")
            
            if new_url != driver.current_url or "passport2.chaoxing.com" not in new_url:
                print("✅ 登录成功，页面已跳转")
                
                # 获取Cookie
                cookies = driver.get_cookies()
                print(f"🍪 获取到Cookie: {len(cookies)} 个")
                for cookie in cookies[:5]:  # 只显示前5个
                    print(f"   {cookie['name']}: {cookie['value'][:20]}...")
                    
            else:
                print("❌ 登录失败，仍在登录页面")
                
                # 查找错误信息
                try:
                    error_elements = driver.find_elements(By.CLASS_NAME, "error")
                    for error in error_elements:
                        if error.text:
                            print(f"   错误: {error.text}")
                except:
                    pass
        
        # 保持浏览器打开一段时间，方便观察
        print("\n⏳ 保持浏览器打开10秒，方便观察...")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
    finally:
        driver.quit()
        print("🔄 浏览器已关闭")

if __name__ == "__main__":
    debug_login_page()
