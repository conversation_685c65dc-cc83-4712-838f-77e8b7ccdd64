#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的座位预约系统：使用Selenium获取Cookie + requests进行预约
"""

import time
import json
import requests
import urllib3
from datetime import datetime, timedelta
from selenium_login_test import SeleniumCookieLogin

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CompleteSeatReservationSystem:
    """完整的座位预约系统"""
    
    def __init__(self):
        self.session = requests.Session()
        self.cookies_loaded = False
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'X-Requested-With': 'XMLHttpRequest',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
    
    def login_with_selenium(self, username, password):
        """使用Selenium登录并获取Cookie"""
        print("="*80)
        print("🚀 开始Selenium登录获取Cookie")
        print("="*80)
        
        selenium_login = SeleniumCookieLogin(headless=True)
        success = selenium_login.login_and_get_cookies(username, password)
        
        if success:
            # 将Cookie设置到requests session
            cookies_dict = selenium_login.get_cookies_dict()
            for name, value in cookies_dict.items():
                self.session.cookies.set(name, value)
            
            self.cookies_loaded = True
            print(f"✅ 成功设置 {len(cookies_dict)} 个Cookie到session")
            return True
        else:
            print("❌ Selenium登录失败")
            return False
    
    def get_reservation_params(self, room_id, seat_num):
        """获取预约所需的参数（token, enc等）"""
        print(f"\n📍 获取预约参数 - 房间:{room_id}, 座位:{seat_num}")

        # 先访问座位系统主页
        main_url = "https://office.chaoxing.com/front/third/apps/seat/index?fidEnc=a76ca29f42cc102d"
        try:
            response = self.session.get(main_url, verify=False, timeout=30)
            print(f"   主页访问状态码: {response.status_code}")
        except Exception as e:
            print(f"⚠️ 访问主页失败: {e}")

        # 访问具体座位页面
        seat_url = f"https://office.chaoxing.com/front/third/apps/seat/code?id={room_id}&seatNum={seat_num}"

        try:
            response = self.session.get(seat_url, verify=False, timeout=30)
            print(f"   座位页面状态码: {response.status_code}")

            if response.status_code == 200:
                # 从页面内容中提取token和其他参数
                page_content = response.text

                # 简单的参数提取（实际可能需要更复杂的解析）
                token = ""
                enc = ""

                # 尝试从页面中提取token
                if 'token' in page_content:
                    import re
                    token_match = re.search(r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']', page_content)
                    if token_match:
                        token = token_match.group(1)
                        print(f"   找到token: {token[:20]}...")

                # 尝试从页面中提取enc
                if 'enc' in page_content:
                    enc_match = re.search(r'enc["\']?\s*[:=]\s*["\']([^"\']+)["\']', page_content)
                    if enc_match:
                        enc = enc_match.group(1)
                        print(f"   找到enc: {enc[:20]}...")

                print("✅ 座位页面访问成功")
                return {'token': token, 'enc': enc}
            else:
                print(f"❌ 座位页面访问失败: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取预约参数时出错: {e}")
            return None
    
    def submit_reservation(self, room_id, seat_num, start_time, end_time, date_str):
        """提交座位预约（使用新的GET API）"""
        print(f"\n🎯 提交座位预约")
        print(f"   房间ID: {room_id}")
        print(f"   座位号: {seat_num}")
        print(f"   开始时间: {start_time}")
        print(f"   结束时间: {end_time}")
        print(f"   日期: {date_str}")
        
        # 构建请求参数
        params = {
            'deptIdEnc': 'a76ca29f42cc102d',  # 这个值可能需要动态获取
            'roomId': room_id,
            'startTime': start_time,
            'endTime': end_time,
            'day': date_str,
            'seatNum': seat_num,
            'captcha': '',  # 暂时为空
            'token': '',    # 需要动态获取
            'enc': ''       # 需要动态获取
        }
        
        # 预约API URL
        url = "https://office.chaoxing.com/data/apps/seat/submit"
        
        try:
            print(f"🌐 发送预约请求...")
            print(f"   URL: {url}")
            print(f"   参数: {params}")
            
            response = self.session.get(url, params=params, verify=False, timeout=30)
            
            print(f"📊 响应信息:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   响应内容: {result}")
                    
                    if result.get('result') == 1:
                        print("✅ 座位预约成功!")
                        return True
                    else:
                        print(f"❌ 座位预约失败: {result.get('msg', '未知错误')}")
                        return False
                        
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效的JSON: {response.text[:200]}")
                    return False
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 提交预约时出错: {e}")
            return False
    
    def reserve_seat(self, config):
        """执行座位预约"""
        username = config['username']
        password = config['password']
        rooms = config['rooms']
        times = config['times']
        
        print("="*80)
        print(f"🎯 开始为用户 {config.get('alias', username)} 预约座位")
        print("="*80)
        
        # 1. 使用Selenium登录获取Cookie
        if not self.login_with_selenium(username, password):
            return False
        
        # 2. 获取明天的日期
        tomorrow = datetime.now() + timedelta(days=1)
        date_str = tomorrow.strftime('%Y-%m-%d')
        
        # 3. 遍历房间和座位进行预约
        for room in rooms:
            room_id = room['roomid']
            seat_ids = room['seatid']
            
            for seat_id in seat_ids:
                print(f"\n🏢 尝试预约房间 {room_id} 的座位 {seat_id}")
                
                # 获取座位信息
                if not self.get_seat_info(room_id, seat_id):
                    continue
                
                # 尝试预约每个时间段
                for i in range(0, len(times), 2):
                    if i + 1 < len(times):
                        start_time = times[i]
                        end_time = times[i + 1]
                        
                        success = self.submit_reservation(
                            room_id, seat_id, start_time, end_time, date_str
                        )
                        
                        if success:
                            print(f"🎉 成功预约座位 {seat_id}，时间 {start_time}-{end_time}")
                            return True
                        else:
                            print(f"❌ 预约座位 {seat_id} 失败，时间 {start_time}-{end_time}")
                            time.sleep(2)  # 等待一下再试下一个
        
        print("❌ 所有座位预约尝试都失败了")
        return False

def main():
    """主函数"""
    print("="*80)
    print("🚀 完整座位预约系统")
    print("="*80)
    
    # 读取配置文件
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if 'reserve' not in config or not config['reserve']:
            print("❌ 配置文件中没有用户信息")
            return
        
        # 创建预约系统
        reservation_system = CompleteSeatReservationSystem()
        
        # 为每个用户执行预约
        for user_config in config['reserve']:
            success = reservation_system.reserve_seat(user_config)
            
            if success:
                print(f"\n🎉 用户 {user_config.get('alias', user_config['username'])} 预约成功!")
            else:
                print(f"\n❌ 用户 {user_config.get('alias', user_config['username'])} 预约失败!")
            
            print("\n" + "="*80)
        
    except FileNotFoundError:
        print("❌ 配置文件 config.json 不存在")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
