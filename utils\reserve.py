# 导入必要的模块和工具函数
from utils import AES_Encrypt, enc, generate_captcha_key  # 导入AES加密、编码和验证码密钥生成函数
import json  # JSON数据处理
import requests  # HTTP请求库
import re  # 正则表达式
import time  # 时间处理
import logging  # 日志记录
import datetime  # 日期时间处理
from urllib3.exceptions import InsecureRequestWarning  # 忽略SSL警告

def get_date(day_offset: int=0):
    """
    获取指定偏移天数的日期字符串

    Args:
        day_offset (int): 日期偏移量，默认为0（今天）

    Returns:
        str: 格式化的日期字符串 (YYYY-MM-DD)
    """
    today = datetime.datetime.now().date()  # 获取今天的日期
    offset_day = today + datetime.timedelta(days=day_offset)  # 计算偏移后的日期
    tomorrow = offset_day.strftime("%Y-%m-%d")  # 格式化为字符串
    return tomorrow

class reserve:
    """
    超星图书馆座位预约系统类
    用于自动化预约图书馆座位
    """

    def __init__(self, sleep_time=0.2, max_attempt=50, enable_slider=False, reserve_next_day=False, debug=False):
        """
        初始化预约系统

        Args:
            sleep_time (float): 请求间隔时间，默认0.2秒
            max_attempt (int): 最大尝试次数，默认50次
            enable_slider (bool): 是否启用滑块验证码，默认False
            reserve_next_day (bool): 是否预约明天，默认False（预约今天）
            debug (bool): 是否开启调试模式，默认False
        """
        # 各种URL地址
        self.login_page = "https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid="  # 登录页面
        self.url = "https://office.chaoxing.com/front/third/apps/seat/code?id={}&seatNum={}"  # 座位页面URL模板
        self.submit_url = "https://office.chaoxing.com/data/apps/seat/submit"  # 提交预约URL
        self.seat_url = "https://office.chaoxing.com/data/apps/seat/getusedtimes"  # 获取座位使用时间URL
        self.login_url = "https://passport2.chaoxing.com/fanyalogin"  # 登录提交URL

        # 状态变量
        self.token = ""  # 页面token
        self.success_times = 0  # 成功次数
        self.fail_dict = []  # 失败记录列表
        self.submit_msg = []  # 提交消息列表
        self.requests = requests.session()  # 创建会话对象
        self.token_pattern = re.compile("token = '(.*?)'")  # token匹配正则表达式

        # 验证码请求头
        self.headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        # 登录请求头
        self.login_headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638",
            "X-Requested-With": "XMLHttpRequest",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Host": "passport2.chaoxing.com"
        }

        # 配置参数
        self.sleep_time = sleep_time  # 请求间隔时间
        self.max_attempt = max_attempt  # 最大尝试次数
        self.enable_slider = enable_slider  # 是否启用滑块验证码
        self.reserve_next_day = reserve_next_day  # 是否预约明天
        self.debug = debug  # 调试模式
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)  # 禁用SSL警告


    # 登录和页面token相关方法
    def _get_page_token(self, url):
        """
        从指定URL页面获取token

        Args:
            url (str): 目标页面URL

        Returns:
            str: 提取的token字符串，如果未找到则返回空字符串
        """
        response = self.requests.get(url=url, verify=False)  # 发送GET请求
        html = response.content.decode('utf-8')  # 解码响应内容
        # 使用正则表达式提取token
        token = re.findall(
            'token: \'(.*?)\'', html)[0] if len(re.findall('token: \'(.*?)\'', html)) > 0 else ""
        return token

    def get_login_status(self):
        """
        获取登录状态，设置登录请求头并访问登录页面
        """
        self.requests.headers = self.login_headers  # 设置登录请求头
        self.requests.get(url=self.login_page, verify=False)  # 访问登录页面

    def login(self, username, password):
        """
        用户登录

        Args:
            username (str): 用户名
            password (str): 密码

        Returns:
            tuple: (登录是否成功, 错误消息)
        """
        username = AES_Encrypt(username)  # AES加密用户名
        password = AES_Encrypt(password)  # AES加密密码
        parm = {
            "fid": -1,  # 学校ID
            "uname": username,  # 加密后的用户名
            "password": password,  # 加密后的密码
            "refer": "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode%3Fid%3D4219%26seatNum%3D380",  # 引用页面
            "t": True  # 时间戳标志
        }
        jsons = self.requests.post(
            url=self.login_url, params=parm, verify=False)  # 发送登录请求
        obj = jsons.json()  # 解析JSON响应
        if obj['status']:  # 登录成功
            logging.info(f"User {username} login successfully")
            return (True, '')
        else:  # 登录失败
            logging.info(f"User {username} login failed. Please check you password and username! ")
            return (False, obj['msg2'])

    # 额外功能：获取房间ID
    def roomid(self, encode):
        """
        获取并打印所有可用的房间信息

        Args:
            encode (str): 部门编码
        """
        url = f"https://office.chaoxing.com/data/apps/seat/room/list?cpage=1&pageSize=100&firstLevelName=&secondLevelName=&thirdLevelName=&deptIdEnc={encode}"
        json_data = self.requests.get(url=url).content.decode('utf-8')  # 获取房间列表数据
        ori_data = json.loads(json_data)  # 解析JSON数据
        # 遍历并打印房间信息
        for i in ori_data["data"]["seatRoomList"]:
            info = f'{i["firstLevelName"]}-{i["secondLevelName"]}-{i["thirdLevelName"]} id为：{i["id"]}'
            print(info)

    # 验证码处理相关方法

    def resolve_captcha(self):
        """
        解决滑块验证码

        Returns:
            str: 验证码验证值，失败时返回空字符串
        """
        logging.info(f"Start to resolve captcha token")  # 开始解决验证码
        captcha_token, bg, tp = self.get_slide_captcha_data()  # 获取验证码数据
        logging.info(f"Successfully get prepared captcha_token {captcha_token}")
        logging.info(f"Captcha Image URL-small {tp}, URL-big {bg}")
        x = self.x_distance(bg, tp)  # 计算滑块距离
        logging.info(f"Successfully calculate the captcha distance {x}")

        # 构建验证参数
        params = {
            "callback": "jQuery33109180509737430778_1716381333117",  # 回调函数名
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",  # 验证码ID
            "type": "slide",  # 验证码类型：滑块
            "token": captcha_token,  # 验证码token
            "textClickArr": json.dumps([{"x": x}]),  # 滑块位置数组
            "coordinate": json.dumps([]),  # 坐标数组
            "runEnv": "10",  # 运行环境
            "version": "1.1.18",  # 版本号
            "_": int(time.time() * 1000)  # 时间戳
        }
        # 发送验证请求
        response = self.requests.get(
            f'https://captcha.chaoxing.com/captcha/check/verification/result', params=params, headers=self.headers)
        # 处理响应文本，移除JSONP包装
        text = response.text.replace('jQuery33109180509737430778_1716381333117(', "").replace(')', "")
        data = json.loads(text)  # 解析JSON数据
        logging.info(f"Successfully resolve the captcha token {data}")
        try:
           validate_val = json.loads(data["extraData"])['validate']  # 提取验证值
           return validate_val
        except KeyError:  # 捕获键错误异常
            logging.info("Can't load validate value. Maybe server return mistake.")
            return ""

    def get_slide_captcha_data(self):
        url = "https://captcha.chaoxing.com/captcha/get/verification/image"
        timestamp = int(time.time() * 1000)
        capture_key, token = generate_captcha_key(timestamp)
        referer = f"https://office.chaoxing.com/front/third/apps/seat/code?id=3993&seatNum=0199"
        params = {
            "callback": f"jQuery33107685004390294206_1716461324846",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "version": "1.1.18",
            "captchaKey": capture_key,
            "token": token,
            "referer": referer,
            "_": timestamp,
            "d": "a",
            "b": "a"
        }
        response = self.requests.get(url=url, params=params, headers=self.headers)
        content = response.text
        
        data = content.replace("jQuery33107685004390294206_1716461324846(",
                            ")").replace(")", "")
        data = json.loads(data)
        captcha_token = data["token"]
        bg = data["imageVerificationVo"]["shadeImage"]
        tp = data["imageVerificationVo"]["cutoutImage"]
        return captcha_token, bg, tp
    
    def x_distance(self, bg, tp):
        import numpy as np
        import cv2
        def cut_slide(slide):
            slider_array = np.frombuffer(slide, np.uint8)
            slider_image = cv2.imdecode(slider_array, cv2.IMREAD_UNCHANGED)
            slider_part = slider_image[:, :, :3]
            mask = slider_image[:, :, 3]
            mask[mask != 0] = 255
            x, y, w, h = cv2.boundingRect(mask)
            cropped_image = slider_part[y:y + h, x:x + w]
            return cropped_image
        c_captcha_headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha-b.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        bgc, tpc = self.requests.get(bg, headers=c_captcha_headers), self.requests.get(tp, headers=c_captcha_headers)
        bg, tp = bgc.content, tpc.content 
        bg_img = cv2.imdecode(np.frombuffer(bg, np.uint8), cv2.IMREAD_COLOR)  
        tp_img = cut_slide(tp)
        bg_edge = cv2.Canny(bg_img, 100, 200)
        tp_edge = cv2.Canny(tp_img, 100, 200)
        bg_pic = cv2.cvtColor(bg_edge, cv2.COLOR_GRAY2RGB)
        tp_pic = cv2.cvtColor(tp_edge, cv2.COLOR_GRAY2RGB)
        res = cv2.matchTemplate(bg_pic, tp_pic, cv2.TM_CCOEFF_NORMED)
        _, _, _, max_loc = cv2.minMaxLoc(res)  
        tl = max_loc
        return tl[0]

    def submit(self, times, roomid, seatid, action, return_detail=False):
        for seat in seatid:
            suc = False
            for attempt in range(self.max_attempt):
                token = self._get_page_token(self.url.format(roomid, seat))
                if self.debug:
                    logging.info(f"Get token: {token}")
                captcha = self.resolve_captcha() if self.enable_slider else "" 
                if self.debug:
                    logging.info(f"Captcha token {captcha}")
                result = self.get_submit(self.submit_url, times=times,token=token, roomid=roomid, seatid=seat, captcha=captcha, action=action)
                # 检查是否已预约或已过期
                if isinstance(result, dict):
                    if result.get('msg') == '该时间段您已有预约！':
                        if self.debug:
                            logging.info('检测到该时间段已有预约，立即终止脚本。')
                        raise Exception('该时间段已有预约，终止脚本')
                    if result.get('msg') == '该时间段已过，不可预约！':
                        if self.debug:
                            logging.info('检测到该时间段已过，不可预约，立即终止脚本。')
                        raise Exception('该时间段已过，不可预约，终止脚本')
                suc = result if isinstance(result, bool) else result.get('success', False)
                if return_detail:
                    params = {
                        'roomId': roomid,
                        'startTime': times[0],
                        'endTime': times[1],
                        'day': str(self._get_reserve_day(action)),
                        'seatNum': seat
                    }
                    if isinstance(result, dict):
                        result = dict(result)
                        result['params'] = params
                        return result
                    else:
                        return {'success': suc, 'params': params}
                if suc:
                    return suc
                time.sleep(self.sleep_time)
        return suc

    def _get_reserve_day(self, action):
        delta_day = 1 if self.reserve_next_day else 0
        day = datetime.date.today() + datetime.timedelta(days=0+delta_day)
        if action:
            day = datetime.date.today() + datetime.timedelta(days=1+delta_day)
        return day

    def get_submit(self, url, times, token, roomid, seatid, captcha="", action=False):
        delta_day = 1 if self.reserve_next_day else 0
        day = datetime.date.today() + datetime.timedelta(days=0+delta_day)  # 预约今天，修改days=1表示预约明天
        if action:
            day = datetime.date.today() + datetime.timedelta(days=1+delta_day)  # 由于action时区问题导致其早+8区一天
        parm = {
            "roomId": roomid,
            "startTime": times[0],
            "endTime": times[1],
            "day": str(day),
            "seatNum": seatid,
            "captcha": captcha,
            "token": token
        }
        if self.debug:
            logging.info(f"submit parameter {parm} ")
        parm["enc"] = enc(parm)
        html = self.requests.post(
            url=url, params=parm, verify=True).content.decode('utf-8')
        self.submit_msg.append(
            times[0] + "~" + times[1] + ':  ' + str(json.loads(html)))
        if self.debug:
            logging.info(json.loads(html))
        # 始终返回完整响应字典
        return json.loads(html)
